# 基金策略回测计算器 - 日志系统

本文档介绍了基金策略回测计算器项目中的日志系统设计和使用方法。

## 日志系统概述

日志系统基于自定义的 `logger.ts` 模块实现，提供了分级日志记录、性能监控和错误跟踪功能。日志系统支持在服务端和客户端组件中使用，可以根据环境配置自动调整日志级别。

## 日志级别

系统支持以下日志级别（按严重程度从低到高排序）：

- **DEBUG**: 详细的调试信息，仅在开发环境中启用
- **INFO**: 一般信息，记录正常操作流程
- **WARN**: 警告信息，表示可能的问题但不影响主要功能
- **ERROR**: 错误信息，表示功能失败或异常
- **NONE**: 禁用所有日志输出

## 日志配置

日志配置在 `src/lib/config.ts` 文件的 `APP_CONFIG.development.logging` 部分定义：

```typescript
logging: {
  // 日志级别: debug, info, warn, error, none
  logLevel: process.env.NODE_ENV === "development" ? "debug" : "info",
  
  // 是否启用控制台日志
  enableConsole: true,
  
  // 是否启用时间戳
  enableTimestamp: true,
  
  // 是否启用源信息
  enableSource: true,
  
  // 是否记录API请求和响应
  logApiCalls: process.env.NODE_ENV === "development",
  
  // 是否记录性能指标
  logPerformance: process.env.NODE_ENV === "development",
  
  // 是否记录用户操作
  logUserActions: false,
}
```

## 日志记录器类型

系统提供了多种预配置的日志记录器，每种针对不同的模块：

- **logger**: 默认日志记录器
- **apiLogger**: API相关操作的日志记录器
- **backtestLogger**: 回测引擎的日志记录器
- **strategyLogger**: 投资策略的日志记录器
- **uiLogger**: UI组件的日志记录器

## 使用方法

### 导入日志记录器

```typescript
// 导入默认日志记录器
import { logger } from "@/lib/logger";

// 导入特定模块的日志记录器
import { apiLogger } from "@/lib/logger";
import { backtestLogger } from "@/lib/logger";
import { strategyLogger } from "@/lib/logger";
import { uiLogger } from "@/lib/logger";
```

### 记录不同级别的日志

```typescript
// 调试信息
logger.debug("这是调试信息", { key: "value" });

// 一般信息
logger.info("操作成功完成", { userId: "123" });

// 警告信息
logger.warn("配置不完整", { missingField: "apiKey" });

// 错误信息
logger.error("操作失败", new Error("网络错误"));
```

### 记录性能指标

```typescript
const startTime = Date.now();
// 执行某些操作...
logger.logPerformance("操作名称", startTime);
```

### 记录API请求和响应

```typescript
// 记录API请求
apiLogger.logApiRequest(url, "GET", params);

// 记录API响应
apiLogger.logApiResponse(url, 200, data);
```

### 记录用户操作

```typescript
// 记录用户操作
uiLogger.logUserAction("点击按钮", { buttonId: "submit" });
```

## 日志格式

日志消息格式如下：

```
[前缀][时间戳][级别][源] 消息 数据
```

例如：

```
[基金策略][2023-06-11T12:34:56.789Z][INFO][用户] 用户操作: 点击按钮 {"buttonId":"submit"}
```

## 最佳实践

1. **选择合适的日志级别**：
   - DEBUG: 用于详细的调试信息，如变量值、函数调用等
   - INFO: 用于记录正常的操作流程和状态变化
   - WARN: 用于记录可能的问题但不影响主要功能
   - ERROR: 用于记录错误和异常

2. **包含上下文信息**：
   - 在日志消息中包含足够的上下文信息，以便于问题定位
   - 使用第二个参数传递结构化数据，而不是拼接字符串

3. **性能考虑**：
   - 在生产环境中，将日志级别设置为 INFO 或更高
   - 避免在循环中过度记录日志
   - 使用条件判断避免不必要的字符串拼接和对象创建

4. **敏感信息处理**：
   - 不要记录密码、令牌等敏感信息
   - 必要时对用户ID等信息进行脱敏处理

## 扩展日志系统

如需扩展日志系统功能，可以修改 `src/lib/logger.ts` 文件，添加新的方法或配置选项。例如：

- 添加日志持久化到文件或数据库
- 集成第三方日志服务
- 添加日志聚合和分析功能
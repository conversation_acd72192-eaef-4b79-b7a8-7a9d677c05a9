# 服务器组件重构文档

## 概述

本文档描述了将基金策略回测应用从客户端渲染重构为服务器组件的过程。这次重构的主要目标是提高性能、改善SEO、减少客户端JavaScript体积，并增强应用的安全性。

## 重构目标

1. **减少客户端JavaScript**: 通过使用服务器组件减少发送到浏览器的JavaScript代码量
2. **提高初始加载性能**: 通过在服务器上渲染HTML加快首次内容绘制
3. **增强数据获取安全性**: 将API调用移至服务器端，避免在客户端暴露API密钥和端点
4. **改善SEO**: 通过服务器渲染提供更好的搜索引擎优化
5. **简化状态管理**: 减少客户端状态管理的复杂性

## 架构变更

### 之前的架构

```
Client Component (BacktestClient)
├── 状态管理 (useState)
├── API调用 (fetchFundData, fetchIndexData)
├── 回测计算 (runBacktest)
└── 渲染UI组件
```

### 新架构

```
Server Component (BacktestPage)
├── 数据获取 (Server Actions)
├── 回测计算 (Server-side)
└── 渲染UI
    ├── Client Component (BacktestForm) - 处理用户交互
    └── Server Component (BacktestResults) - 展示结果
```

## 主要变更

1. **新的服务器组件**:
   - `BacktestPage`: 主页面服务器组件
   - `BacktestResults`: 回测结果服务器组件

2. **客户端组件更新**:
   - `BacktestForm`: 处理用户输入和表单提交
   - 其他交互组件保持为客户端组件

3. **数据流变更**:
   - 使用URL查询参数在服务器组件之间传递状态
   - 使用Server Actions进行数据获取
   - 在服务器上执行回测计算

4. **API调用安全性**:
   - 所有第三方API调用都在服务器端执行
   - API密钥和敏感配置只在服务器端可用

## 性能优化

1. **流式渲染**:
   - 使用React Suspense实现流式渲染
   - 关键内容优先加载

2. **并行数据获取**:
   - 在服务器上并行获取数据
   - 减少瀑布式请求

3. **缓存策略**:
   - 实现服务器端缓存减少重复请求
   - 使用Next.js内置缓存机制

## 开发指南

### 创建新的服务器组件

```tsx
// 服务器组件示例
export default async function ServerComponent() {
  // 直接使用await获取数据
  const data = await fetchDataOnServer();
  
  return <div>{/* 渲染数据 */}</div>;
}
```

### 在服务器组件中使用客户端组件

```tsx
// 服务器组件
import ClientComponent from './ClientComponent';

export default async function ServerComponent() {
  const data = await fetchDataOnServer();
  
  return (
    <div>
      {/* 将数据作为props传递给客户端组件 */}
      <ClientComponent initialData={data} />
    </div>
  );
}
```

### 使用Server Actions

```tsx
// actions.ts
"use server";

export async function serverAction(data: FormData) {
  // 在服务器上执行操作
  const result = await processData(data);
  return result;
}

// 客户端组件
import { serverAction } from './actions';

export default function ClientComponent() {
  // 使用Server Action
  async function handleSubmit(formData: FormData) {
    await serverAction(formData);
  }
  
  return <form action={handleSubmit}>{/* 表单内容 */}</form>;
}
```

## 最佳实践

1. **组件分类**:
   - 将组件明确分为服务器组件和客户端组件
   - 客户端组件使用"use client"指令

2. **数据获取**:
   - 在服务器组件中获取数据
   - 使用Server Actions处理表单提交和数据变更

3. **状态管理**:
   - 将全局状态移至URL参数
   - 本地UI状态保留在客户端组件中

4. **错误处理**:
   - 使用React Error Boundary捕获错误
   - 实现优雅的降级体验

## 注意事项

1. **服务器组件限制**:
   - 服务器组件不能使用useState、useEffect等hooks
   - 服务器组件不能使用浏览器API

2. **客户端组件边界**:
   - 一旦导入了客户端组件，父组件也必须是客户端组件
   - 谨慎设计组件边界，避免不必要的客户端渲染

3. **数据传递**:
   - 从服务器组件到客户端组件的数据必须是可序列化的
   - 复杂对象需要在传递前进行序列化
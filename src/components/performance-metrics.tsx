import { formatCurrency, formatPercent } from "@/lib/utilities";

interface PerformanceMetricsProperties {
  performance: {
    totalReturn: number;
    annualizedReturn: number;
    volatility: number;
    sharpeRatio: number;
    maxDrawdown: number;
    totalInvestment: number;
    finalValue: number;
  };
}

/**
 * Component to display performance metrics
 */
export default function PerformanceMetrics({ performance }: PerformanceMetricsProperties) {
  const metrics = [
    {
      name: "总收益率",
      value: formatPercent(performance.totalReturn),
      description: "投资期间的总收益百分比",
      positive: performance.totalReturn > 0,
    },
    {
      name: "年化收益率",
      value: formatPercent(performance.annualizedReturn),
      description: "按年计算的平均收益率",
      positive: performance.annualizedReturn > 0,
    },
    {
      name: "波动率",
      value: formatPercent(performance.volatility),
      description: "收益率的标准差，衡量风险",
      positive: false,
    },
    {
      name: "夏普比率",
      value: performance.sharpeRatio.toFixed(2),
      description: "风险调整后的收益指标",
      positive: performance.sharpeRatio > 1,
    },
    {
      name: "最大回撤",
      value: formatPercent(performance.maxDrawdown),
      description: "投资期间的最大亏损幅度",
      positive: false,
    },
    {
      name: "总投资金额",
      value: formatCurrency(performance.totalInvestment),
      description: "投资期间的总投入金额",
      positive: false,
    },
    {
      name: "最终价值",
      value: formatCurrency(performance.finalValue),
      description: "投资期末的总价值",
      positive: performance.finalValue > performance.totalInvestment,
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {metrics.map((metric) => (
        <div
          key={metric.name}
          className="bg-gray-50 rounded-lg p-4 border border-gray-200"
        >
          <div className="text-sm text-gray-500">{metric.name}</div>
          <div
            className={`text-xl font-semibold ${
              metric.positive ? "text-green-600" : metric.positive === false ? "text-gray-800" : ""
            }`}
          >
            {metric.value}
          </div>
          <div className="text-xs text-gray-400 mt-1">{metric.description}</div>
        </div>
      ))}
    </div>
  );
}
"use client";

import { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import FundSelector from "@/components/fund-selector";
import IndexSelector from "@/components/index-selector";
import ParameterInputs from "@/components/parameter-inputs";
import StrategySelector from "@/components/strategy-selector";
import { uiLogger as logger } from "@/lib/logger";
import type { Fund, Strategy } from "@/types/fund";

interface BacktestFormProperties {
  strategies: Strategy[];
}

/**
 * Client component for the backtest form
 * Handles user interactions and form state
 */
export default function BacktestForm({ strategies }: BacktestFormProperties) {
  const router = useRouter();
  const searchParameters = useSearchParams();
  
  // Get initial values from URL if available
  const initialFundId = searchParameters.get("fundId");
  const initialStrategyId = searchParameters.get("strategyId");
  
  const [selectedFund, setSelectedFund] = useState<Fund | null>(null);
  const [selectedIndex, setSelectedIndex] = useState<any | null>(null);
  const [selectedStrategy, setSelectedStrategy] = useState<Strategy | null>(
    initialStrategyId 
      ? strategies.find(s => s.id === initialStrategyId) || null 
      : null
  );
  const [parameters, setParameters] = useState<Record<string, unknown>>({
    startDate: searchParameters.get("startDate") || "2020-01-01",
    endDate: searchParameters.get("endDate") || "2024-01-01",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Handle form submission
  const handleRunBacktest = async () => {
    if (!selectedFund || !selectedStrategy) {
      logger.warn("回测参数不完整", { 
        hasFund: !!selectedFund, 
        hasStrategy: !!selectedStrategy 
      });
      setError("请选择基金和投资策略");
      return;
    }

    // Validate required parameters
    const requiredParameters = Object.entries(selectedStrategy.parameterSchema)
      .filter(([, parameter]) => parameter.required)
      .map(([key]) => key);

    const missingParameters = requiredParameters.filter(
      (key) => !parameters[key] || parameters[key] === ""
    );
    
    if (missingParameters.length > 0) {
      logger.warn("缺少必填参数", { 
        missingParameters,
        strategy: selectedStrategy.id
      });
      setError("请填写所有必填参数");
      return;
    }

    logger.debug("回测参数验证通过", { 
      fund: selectedFund.code,
      strategy: selectedStrategy.id,
      parameters
    });
    
    setIsLoading(true);
    setError(null);

    try {
      // Build URL parameters for server-side processing
      const parameters_ = new URLSearchParams({
        fundId: selectedFund.id,
        fundCode: selectedFund.code,
        strategyId: selectedStrategy.id,
        ...Object.entries(parameters).reduce((accumulator, [key, value]) => {
          accumulator[key] = String(value);
          return accumulator;
        }, {} as Record<string, string>)
      });

      if (selectedIndex) {
        parameters_.append("indexId", selectedIndex.id);
        parameters_.append("indexCode", selectedIndex.code);
      }

      // Navigate to the same page with parameters
      router.push(`/?${parameters_.toString()}`);
      
      logger.info("回测请求已提交", { 
        fund: selectedFund.name,
        strategy: selectedStrategy.name,
        startDate: parameters.startDate,
        endDate: parameters.endDate
      });
    } catch (error_) {
      const errorMessage = error_ instanceof Error ? error_.message : String(error_);
      logger.error("回测请求提交失败", { 
        error: errorMessage
      });
      setError("回测请求提交失败，请稍后重试");
    } finally {
      setIsLoading(false);
    }
  };

  // Reset all state
  const handleReset = () => {
    logger.info("用户重置表单");
    setSelectedFund(null);
    setSelectedIndex(null);
    setSelectedStrategy(null);
    setParameters({
      startDate: "2020-01-01",
      endDate: "2024-01-01",
    });
    setError(null);
    router.push("/");
  };

  return (
    <>
      {/* 基金选择 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <FundSelector
          selectedFund={selectedFund}
          onFundSelect={setSelectedFund}
        />
      </div>

      {/* 指数选择 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <IndexSelector
          selectedIndex={selectedIndex}
          onIndexSelect={setSelectedIndex}
        />
      </div>

      {/* 策略选择 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <StrategySelector
          strategies={strategies}
          selectedStrategy={selectedStrategy}
          onStrategySelect={setSelectedStrategy}
        />
      </div>

      {/* 参数设置 */}
      {selectedStrategy ? (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <ParameterInputs
            strategy={selectedStrategy}
            parameters={parameters}
            onParametersChange={setParameters}
          />
        </div>
      ) : null}

      {/* 执行回测按钮 */}
      {selectedFund && selectedStrategy ? (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex space-x-4">
            <button
              onClick={handleRunBacktest}
              disabled={isLoading}
              className={`flex-1 py-3 px-4 rounded-lg font-medium text-white transition-colors ${
                isLoading
                  ? "bg-gray-400 cursor-not-allowed"
                  : "bg-blue-600 hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              }`}
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <svg
                    className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                  执行回测中...
                </div>
              ) : (
                "开始回测"
              )}
            </button>
            
            <button
              onClick={handleReset}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              重置
            </button>
          </div>

          {error ? (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center">
                <svg
                  className="w-5 h-5 text-red-400 mr-2"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clipRule="evenodd"
                  />
                </svg>
                <span className="text-sm text-red-800">{error}</span>
              </div>
            </div>
          ) : null}
        </div>
      ) : null}
    </>
  );
}
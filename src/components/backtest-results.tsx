import { Suspense } from "react";
import { notFound } from "next/navigation";
import BacktestChart from "@/components/backtest-chart";
import PerformanceMetrics from "@/components/performance-metrics";
import TransactionTable from "@/components/transaction-table";
import LoadingSpinner from "@/components/ui/loading-spinner";
import { 
  fetchFundDataAction, 
  fetchIndexDataAction,
  validateFundAction
} from "@/lib/actions";
import { createBacktestEngine, runBacktest } from "@/lib/backtest";
import { getStrategyByIdAction } from "@/lib/strategies";
import { logger } from "@/lib/logger";
import type { Fund, BacktestResult } from "@/types/fund";

/**
 * Server component for displaying backtest results
 * Fetches data and performs calculations on the server
 */
export default async function BacktestResults({
  searchParams
}: {
  searchParams?: {
    fundId?: string;
    fundCode?: string;
    strategyId?: string;
    indexId?: string;
    indexCode?: string;
    startDate?: string;
    endDate?: string;
    [key: string]: string | undefined;
  }
}) {
  // If no search params, show empty state
  if (!searchParams?.fundCode || !searchParams?.strategyId) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="text-center py-12">
          <svg
            className="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
            />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">
            暂无回测结果
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            请选择基金和投资策略，设置参数后开始回测
          </p>
        </div>
      </div>
    );
  }

  try {
    // Validate fund code
    const isValidFund = await validateFundAction(searchParams.fundCode);
    if (!isValidFund) {
      logger.error("无效的基金代码", { fundCode: searchParams.fundCode });
      return (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="text-center py-12">
            <svg
              className="mx-auto h-12 w-12 text-red-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              无效的基金代码
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              请检查基金代码是否正确
            </p>
          </div>
        </div>
      );
    }

    // Get strategy
    const strategy = await getStrategyByIdAction(searchParams.strategyId);
    if (!strategy) {
      logger.error("无效的策略ID", { strategyId: searchParams.strategyId });
      return notFound();
    }

    // Create fund object
    const fund: Fund = {
      id: searchParams.fundId || searchParams.fundCode,
      code: searchParams.fundCode,
      name: `基金${searchParams.fundCode}`,
      type: "hybrid",
    };

    // Extract parameters from search params
    const parameters = {
      startDate: searchParams.startDate || "2020-01-01",
      endDate: searchParams.endDate || "2024-01-01",
      initialAmount: Number(searchParams.initialAmount || 10_000),
      monthlyAmount: Number(searchParams.monthlyAmount || 1000),
      frequency: searchParams.frequency || "monthly",
      // Add other parameters from search params
      ...Object.entries(searchParams)
        .filter(([key]) => !["fundId", "fundCode", "strategyId", "indexId", "indexCode", "startDate", "endDate"].includes(key))
        .reduce((accumulator, [key, value]) => {
          // Try to convert to number if possible
          const numberValue = Number(value);
          accumulator[key] = isNaN(numberValue) ? value : numberValue;
          return accumulator;
        }, {} as Record<string, any>)
    };

    // Fetch fund data
    const fundData = await fetchFundDataAction(
      searchParams.fundCode,
      parameters.startDate,
      parameters.endDate
    );

    // Fetch index data if available
    let indexData;
    if (searchParams.indexCode) {
      indexData = await fetchIndexDataAction(
        searchParams.indexCode,
        parameters.startDate,
        parameters.endDate
      );
    }

    // Create backtest engine and run backtest
    const engineConfig = createBacktestEngine(fundData, indexData);
    const result = await runBacktest(
      engineConfig,
      fund,
      parameters
    );

    return (
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            回测结果: {fund.name} - {strategy.name}
          </h2>
          <Suspense fallback={<LoadingSpinner />}>
            <BacktestChart result={result} />
          </Suspense>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            性能指标
          </h3>
          <PerformanceMetrics performance={result.performance} />
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            交易记录
          </h3>
          <TransactionTable timeline={result.timeline} />
        </div>
      </div>
    );
  } catch (error) {
    logger.error("回测结果生成失败", { error });
    
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="text-center py-12">
          <svg
            className="mx-auto h-12 w-12 text-red-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
            />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">
            回测执行失败
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            请检查参数设置或稍后重试
          </p>
        </div>
      </div>
    );
  }
}
"use server";

import type { BaiduApiResponse, CSIndexApiResponse } from "@/lib/api/types";
import {
  buildApiUrl,
  buildCSIndexApiUrl,
  parseFundData,
  parseCSIndexData,
  parsePerformanceData,
  withRetry,
  withTimeout,
} from "@/lib/api/utilities";
import type { Fund, FundData, IndexData } from "@/types/fund";
import { logger } from "./logger";

/**
 * Server action to get all funds
 * 返回空数组，只有用户搜索时才显示基金选项
 */
export async function getFundsAction(): Promise<Fund[]> {
  try {
    // 不再返回默认基金数据，用户需要主动搜索
    return [];
  } catch (error) {
    console.error("Failed to load funds:", error);
    throw new Error("Failed to load funds");
  }
}

/**
 * Server action to search funds using CSRC API
 */
export async function searchFundsAction(query: string): Promise<Fund[]> {
  try {
    if (!query.trim()) {
      return [];
    }

    // 构建CSRC API请求参数
    const aoData = [
      { name: "sEcho", value: 1 },
      { name: "iColumns", value: 11 },
      { name: "sColumns", value: ",,,,,,,,,," },
      { name: "iDisplayStart", value: 0 },
      { name: "iDisplayLength", value: 20 },
      { name: "mDataProp_0", value: "fundCode" },
      { name: "mDataProp_1", value: "fundName" },
      { name: "mDataProp_2", value: "fundType" },
      { name: "mDataProp_3", value: "fundCompany" },
      { name: "mDataProp_4", value: "fundManager" },
      { name: "mDataProp_5", value: "establishDate" },
      { name: "mDataProp_6", value: "netAssetValue" },
      { name: "mDataProp_7", value: "accumulatedValue" },
      { name: "mDataProp_8", value: "valueDate" },
      { name: "mDataProp_9", value: "dailyGrowthRate" },
      { name: "mDataProp_10", value: "operation" },
      { name: "fundName", value: query },
    ];

    const url = `http://eid.csrc.gov.cn/fund/disclose/getPublicFundJZInfoMore.do?aoData=${encodeURIComponent(JSON.stringify(aoData))}&_=${Date.now()}`;

    const response = await fetch(url, {
      method: "GET",
      headers: {
        Accept: "application/json, text/javascript, */*; q=0.01",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
        "Content-Type": "application/json",
        Pragma: "no-cache",
        Referer: "http://eid.csrc.gov.cn/fund/disclose/index.html",
        "User-Agent":
          "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "X-Requested-With": "XMLHttpRequest",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();

    if (data.aaData && Array.isArray(data.aaData)) {
      return data.aaData.map((item: any[]) => ({
        id: item[0],
        name: item[1],
        code: item[0],
        type: "hybrid" as const, // 默认类型，可以根据基金名称推断
        company: item[3],
        manager: item[4],
        establishDate: item[5],
      }));
    }

    return [];
  } catch (error) {
    console.error("Failed to search funds:", error);
    throw new Error("Failed to search funds");
  }
}

/**
 * Server action to search indices using CSIndex API
 */
export async function searchIndicesAction(query: string): Promise<any[]> {
  try {
    if (!query.trim()) {
      return [];
    }

    const requestBody = {
      pageNum: 1,
      pageSize: 20,
      indexName: query,
      classify: "",
      currency: "",
      region: "",
    };

    const response = await fetch(
      "https://www.csindex.com.cn/csindex-home/index-list/query-index-item",
      {
        method: "POST",
        headers: {
          Accept: "application/json, text/plain, */*",
          "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
          "Cache-Control": "no-cache",
          Connection: "keep-alive",
          "Content-Type": "application/json;charset=UTF-8",
          Origin: "https://www.csindex.com.cn",
          Pragma: "no-cache",
          Referer: "https://www.csindex.com.cn/csindex-home/index-list",
          "User-Agent":
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        },
        body: JSON.stringify(requestBody),
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();

    if (data.code === "000000" && data.data?.list) {
      return data.data.list.map((item: any) => ({
        id: item.indexCode,
        name: item.indexName,
        code: item.indexCode,
        description: item.indexDesc,
        type: item.indexType,
        classify: item.classify,
        currency: item.currency,
        region: item.region,
        publishDate: item.publishDate,
        baseDate: item.baseDate,
        basePoint: item.basePoint,
      }));
    }

    return [];
  } catch (error) {
    console.error("Failed to search indices:", error);
    throw new Error("Failed to search indices");
  }
}

/**
 * Server action to validate fund code using Baidu API
 */
export async function validateFundAction(fundCode: string): Promise<boolean> {
  try {
    if (!/^\d{6}$/.test(fundCode)) {
      return false;
    }

    const url = buildApiUrl(fundCode);
    const response = await fetch(url, {
      method: "GET",
      headers: {
        "User-Agent":
          "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
        Accept: "application/json, text/plain, */*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Cache-Control": "no-cache",
        Pragma: "no-cache",
      },
    });

    if (!response.ok) {
      return false;
    }

    const data = await response.json();
    return data.ResultCode === "0";
  } catch (error) {
    console.error("Failed to validate fund:", error);
    return false;
  }
}

/**
 * Server action to fetch fund data using Baidu API
 */
export async function fetchFundDataAction(
  fundCode: string,
  startDate?: string,
  endDate?: string
): Promise<FundData[]> {
  const startTime = Date.now();
  logger.info("开始获取基金数据", { fundCode, startDate, endDate });
  
  try {
    const url = buildApiUrl(fundCode);
    logger.debug("构建API URL", { url });

    const fetchWithRetry = () =>
      withRetry(
        async () => {
          logger.debug("发送API请求", { url });
          const fetchStartTime = Date.now();
          
          const response = await fetch(url, {
            method: "GET",
            headers: {
              "User-Agent":
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
              Accept: "application/json, text/plain, */*",
              "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
              "Cache-Control": "no-cache",
              Pragma: "no-cache",
            },
          });

          logger.debug("收到API响应", { 
            status: response.status, 
            statusText: response.statusText,
            duration: Date.now() - fetchStartTime
          });

          if (!response.ok) {
            logger.error("API请求失败", { 
              status: response.status, 
              statusText: response.statusText 
            });
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const data = await response.json();
          logger.debug("解析API响应JSON完成");

          if (data.ResultCode !== "0") {
            logger.error("API返回错误码", { resultCode: data.ResultCode });
            throw new Error(`API错误: ${data.ResultCode}`);
          }

          return data as BaiduApiResponse;
        },
        { maxRetries: 3, delay: 1000 }
      );

    logger.debug("开始执行API请求（带超时和重试）");
    const rawData = await withTimeout(fetchWithRetry(), 10_000);
    logger.debug("API请求成功，开始解析数据");
    
    const parsedData = parseFundData(rawData);
    logger.debug("数据解析完成", { dataPoints: parsedData.length });

    // 转换为项目内部格式
    const fundData = parsedData.map((item) => ({
      date: item.date,
      netAssetValue: item.netAssetValue,
      accumulatedValue: item.accumulatedValue,
      dailyGrowthRate: 0, // 计算日增长率或从其他字段获取
    }));
    logger.debug("数据格式转换完成");

    // 应用日期过滤
    if (startDate || endDate) {
      logger.debug("应用日期过滤", { startDate, endDate });
      const filteredData = fundData.filter((item) => {
        const itemDate = new Date(item.date);
        return !(
          (startDate && itemDate < new Date(startDate)) ||
          (endDate && itemDate > new Date(endDate))
        );
      });
      
      logger.info("基金数据获取成功（已过滤）", { 
        fundCode, 
        totalPoints: fundData.length,
        filteredPoints: filteredData.length,
        duration: Date.now() - startTime
      });
      
      return filteredData;
    }

    logger.info("基金数据获取成功", { 
      fundCode, 
      dataPoints: fundData.length,
      firstDate: fundData[0]?.date,
      lastDate: fundData.at(-1)?.date,
      duration: Date.now() - startTime
    });
    
    return fundData;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("获取基金数据失败", { 
      fundCode, 
      error: errorMessage,
      duration: Date.now() - startTime
    });
    throw new Error(`获取基金数据失败: ${errorMessage}`);
  }
}

/**
 * Server action to fetch index data using CSIndex API
 */
export async function fetchIndexDataAction(
  indexCode: string,
  startDate: string,
  endDate: string
): Promise<IndexData[]> {
  try {
    const url = buildCSIndexApiUrl(indexCode, startDate, endDate);

    const fetchWithRetry = () =>
      withRetry(
        async () => {
          const response = await fetch(url, {
            method: "GET",
            headers: {
              Accept: "application/json, text/plain, */*",
              "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
              "Cache-Control": "no-cache",
              Connection: "keep-alive",
              Pragma: "no-cache",
              Referer: "https://www.csindex.com.cn/",
              "User-Agent":
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            },
          });

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const data = await response.json();

          if (data.code !== "000000") {
            throw new Error(`API错误: ${data.code} - ${data.message}`);
          }

          return data as CSIndexApiResponse;
        },
        { maxRetries: 3, delay: 1000 }
      );

    const rawData = await withTimeout(fetchWithRetry(), 10_000);
    const parsedData = parseCSIndexData(rawData);

    // 转换为项目内部格式
    return parsedData.map((item) => ({
      date: item.time,
      value: item.close,
      change: item.range || 0,
      changePercent: item.ratio || 0,
    }));
  } catch (error) {
    console.error("Failed to fetch index data:", error);
    throw new Error("Failed to fetch index data");
  }
}

/**
 * Server action to fetch fund performance comparison data using Baidu API
 */
export async function fetchFundPerformanceAction(
  fundCode: string,
  months = 12
): Promise<any> {
  try {
    const url = buildApiUrl(fundCode, {
      dataType: "ai",
      months,
      source: "qieman",
    });

    const fetchWithRetry = () =>
      withRetry(
        async () => {
          const response = await fetch(url, {
            method: "GET",
            headers: {
              "User-Agent":
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
              Accept: "application/json, text/plain, */*",
              "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
              "Cache-Control": "no-cache",
              Pragma: "no-cache",
            },
          });

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const data = await response.json();

          if (data.ResultCode !== "0") {
            throw new Error(`API错误: ${data.ResultCode}`);
          }

          return data as BaiduApiResponse;
        },
        { maxRetries: 3, delay: 1000 }
      );

    const rawData = await withTimeout(fetchWithRetry(), 10_000);
    return parsePerformanceData(rawData);
  } catch (error) {
    console.error("Failed to fetch fund performance:", error);
    throw new Error("Failed to fetch fund performance");
  }
}

// 日志工具模块
import { APP_CONFIG } from "./config";

/**
 * 日志级别枚举
 */
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  NONE = 4,
}

/**
 * 日志配置接口
 */
interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableTimestamp: boolean;
  enableSource: boolean;
  prefix?: string;
}

/**
 * 默认日志配置
 */
const DEFAULT_CONFIG: LoggerConfig = {
  level: APP_CONFIG.development.debug ? LogLevel.DEBUG : LogLevel.INFO,
  enableConsole: true,
  enableTimestamp: true,
  enableSource: true,
  prefix: "基金策略",
};

/**
 * 将字符串日志级别转换为枚举值
 */
function getLogLevelFromString(level: string): LogLevel {
  switch (level.toLowerCase()) {
    case "debug": {
      return LogLevel.DEBUG;
    }
    case "info": {
      return LogLevel.INFO;
    }
    case "warn": {
      return LogLevel.WARN;
    }
    case "error": {
      return LogLevel.ERROR;
    }
    case "none": {
      return LogLevel.NONE;
    }
    default: {
      return LogLevel.INFO;
    }
  }
}

/**
 * 日志工具类
 */
class Logger {
  private config: LoggerConfig;

  constructor(config: Partial<LoggerConfig> = {}) {
    // 从环境配置中获取日志级别
    const configLogLevel = getLogLevelFromString(APP_CONFIG.development.logLevel);
    
    this.config = {
      ...DEFAULT_CONFIG,
      level: configLogLevel,
      ...config,
    };
  }

  /**
   * 格式化日志消息
   */
  private formatMessage(level: string, message: string, source?: string): string {
    const parts: string[] = [];
    
    if (this.config.prefix) {
      parts.push(`[${this.config.prefix}]`);
    }
    
    if (this.config.enableTimestamp) {
      parts.push(`[${new Date().toISOString()}]`);
    }
    
    parts.push(`[${level}]`);
    
    if (this.config.enableSource && source) {
      parts.push(`[${source}]`);
    }
    
    parts.push(message);
    
    return parts.join(" ");
  }

  /**
   * 记录调试级别日志
   */
  debug(message: string, data?: any, source?: string): void {
    if (this.config.level <= LogLevel.DEBUG) {
      const formattedMessage = this.formatMessage("DEBUG", message, source);
      if (this.config.enableConsole) {
        console.debug(formattedMessage, data === undefined ? "" : data);
      }
    }
  }

  /**
   * 记录信息级别日志
   */
  info(message: string, data?: any, source?: string): void {
    if (this.config.level <= LogLevel.INFO) {
      const formattedMessage = this.formatMessage("INFO", message, source);
      if (this.config.enableConsole) {
        console.info(formattedMessage, data === undefined ? "" : data);
      }
    }
  }

  /**
   * 记录警告级别日志
   */
  warn(message: string, data?: any, source?: string): void {
    if (this.config.level <= LogLevel.WARN) {
      const formattedMessage = this.formatMessage("WARN", message, source);
      if (this.config.enableConsole) {
        console.warn(formattedMessage, data === undefined ? "" : data);
      }
    }
  }

  /**
   * 记录错误级别日志
   */
  error(message: string, error?: any, source?: string): void {
    if (this.config.level <= LogLevel.ERROR) {
      const formattedMessage = this.formatMessage("ERROR", message, source);
      if (this.config.enableConsole) {
        console.error(formattedMessage, error === undefined ? "" : error);
      }
    }
  }

  /**
   * 记录API请求日志
   */
  logApiRequest(url: string, method: string, parameters?: any): void {
    this.debug(`API请求: ${method} ${url}`, parameters, "API");
  }

  /**
   * 记录API响应日志
   */
  logApiResponse(url: string, status: number, data?: any): void {
    this.debug(`API响应: ${status} ${url}`, data, "API");
  }

  /**
   * 记录性能日志
   */
  logPerformance(operation: string, startTime: number): void {
    const duration = Date.now() - startTime;
    this.debug(`性能: ${operation} 耗时 ${duration}ms`, null, "性能");
  }

  /**
   * 记录用户操作日志
   */
  logUserAction(action: string, details?: any): void {
    this.info(`用户操作: ${action}`, details, "用户");
  }

  /**
   * 记录策略执行日志
   */
  logStrategy(strategy: string, parameters?: any, result?: any): void {
    this.debug(`策略执行: ${strategy}`, { params: parameters, result }, "策略");
  }

  /**
   * 创建子日志记录器
   */
  createSubLogger(source: string): Logger {
    return new Logger({
      ...this.config,
      prefix: `${this.config.prefix}:${source}`,
    });
  }
}

// 创建默认日志记录器实例
export const logger = new Logger();

// 创建特定模块的日志记录器
export const apiLogger = logger.createSubLogger("API");
export const backtestLogger = logger.createSubLogger("回测");
export const strategyLogger = logger.createSubLogger("策略");
export const uiLogger = logger.createSubLogger("UI");

// 导出默认日志记录器
export default logger;
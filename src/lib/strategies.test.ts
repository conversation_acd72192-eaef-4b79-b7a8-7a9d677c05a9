import { describe, it, expect } from "vitest";

import { strategies } from "@/lib/strategyData";
import type { StrategyType } from "@/types/fund";

// Helper functions for tests
async function getStrategies() {
  return Object.values(strategies);
}

async function getStrategy(id: StrategyType) {
  return strategies[id];
}

describe("Strategies", () => {
  describe("strategies配置", () => {
    it("应该包含所有策略类型", () => {
      const expectedStrategies: StrategyType[] = [
        "fixed_amount",
        "value_averaging",
        "smart_fixed",
        "grid_trading",
        "momentum",
        "mean_reversion",
      ];

      for (const strategyType of expectedStrategies) {
        expect(strategies).toHaveProperty(strategyType);
      }
    });

    it("应该为每个策略提供完整的配置", () => {
      for (const strategy of Object.values(strategies)) {
        expect(strategy).toHaveProperty("id");
        expect(strategy).toHaveProperty("name");
        expect(strategy).toHaveProperty("description");
        expect(strategy).toHaveProperty("riskLevel");
        expect(strategy).toHaveProperty("complexity");
        expect(strategy).toHaveProperty("parameterSchema");

        expect(typeof strategy.id).toBe("string");
        expect(typeof strategy.name).toBe("string");
        expect(typeof strategy.description).toBe("string");
        expect(["low", "medium", "high"]).toContain(strategy.riskLevel);
        expect(["beginner", "intermediate", "advanced"]).toContain(
          strategy.complexity
        );
        expect(typeof strategy.parameterSchema).toBe("object");
      }
    });
  });

  describe("定投策略 (fixed_amount)", () => {
    const strategy = strategies.fixed_amount;

    it("应该有正确的基本信息", () => {
      expect(strategy.id).toBe("fixed_amount");
      expect(strategy.name).toBe("定期定额投资");
      expect(strategy.riskLevel).toBe("low");
      expect(strategy.complexity).toBe("beginner");
    });

    it("应该有必需的参数配置", () => {
      const schema = strategy.parameterSchema;

      expect(schema).toHaveProperty("startDate");
      expect(schema).toHaveProperty("endDate");
      expect(schema).toHaveProperty("initialAmount");
      expect(schema).toHaveProperty("monthlyAmount");
      expect(schema).toHaveProperty("frequency");

      expect(schema.startDate.required).toBe(true);
      expect(schema.endDate.required).toBe(true);
      expect(schema.initialAmount.required).toBe(true);
      expect(schema.monthlyAmount.required).toBe(true);
      expect(schema.frequency.required).toBe(true);
    });

    it("应该有正确的默认值", () => {
      const schema = strategy.parameterSchema;

      expect(schema.startDate.defaultValue).toBe("2020-01-01");
      expect(schema.endDate.defaultValue).toBe("2024-01-01");
      expect(schema.initialAmount.defaultValue).toBe(10_000);
      expect(schema.monthlyAmount.defaultValue).toBe(1000);
      expect(schema.frequency.defaultValue).toBe("monthly");
    });
  });

  describe("价值平均策略 (value_averaging)", () => {
    const strategy = strategies.value_averaging;

    it("应该有正确的基本信息", () => {
      expect(strategy.id).toBe("value_averaging");
      expect(strategy.name).toBe("价值平均策略");
      expect(strategy.riskLevel).toBe("medium");
      expect(strategy.complexity).toBe("intermediate");
    });

    it("应该有特定的参数配置", () => {
      const schema = strategy.parameterSchema;

      expect(schema).toHaveProperty("targetGrowthRate");
      expect(schema).toHaveProperty("maxInvestment");

      expect(schema.targetGrowthRate.min).toBe(1);
      expect(schema.targetGrowthRate.max).toBe(30);
      expect(schema.targetGrowthRate.defaultValue).toBe(8);

      expect(schema.maxInvestment.min).toBe(1000);
      expect(schema.maxInvestment.defaultValue).toBe(5000);
    });
  });

  describe("网格交易策略 (grid_trading)", () => {
    const strategy = strategies.grid_trading;

    it("应该有正确的基本信息", () => {
      expect(strategy.id).toBe("grid_trading");
      expect(strategy.name).toBe("网格交易");
      expect(strategy.riskLevel).toBe("high");
      expect(strategy.complexity).toBe("advanced");
    });

    it("应该有网格特定的参数", () => {
      const schema = strategy.parameterSchema;

      expect(schema).toHaveProperty("gridCount");
      expect(schema).toHaveProperty("priceRangeMin");
      expect(schema).toHaveProperty("priceRangeMax");
      expect(schema).toHaveProperty("investmentPerGrid");
      expect(schema).toHaveProperty("rebalanceFrequency");

      expect(schema.gridCount.min).toBe(5);
      expect(schema.gridCount.max).toBe(50);
      expect(schema.gridCount.defaultValue).toBe(10);
    });

    it("应该有可选的风控参数", () => {
      const schema = strategy.parameterSchema;

      expect(schema).toHaveProperty("stopLoss");
      expect(schema).toHaveProperty("takeProfit");

      expect(schema.stopLoss.required).toBe(false);
      expect(schema.takeProfit.required).toBe(false);
    });
  });

  describe("智能定投策略 (smart_fixed)", () => {
    const strategy = strategies.smart_fixed;

    it("应该有正确的基本信息", () => {
      expect(strategy.id).toBe("smart_fixed");
      expect(strategy.name).toBe("智能定投");
      expect(strategy.riskLevel).toBe("medium");
      expect(strategy.complexity).toBe("intermediate");
    });

    it("应该有智能调整参数", () => {
      const schema = strategy.parameterSchema;

      expect(schema).toHaveProperty("baseAmount");
      expect(schema).toHaveProperty("adjustmentFactor");
      expect(schema).toHaveProperty("valuationMetric");

      expect(schema.adjustmentFactor.min).toBe(0.1);
      expect(schema.adjustmentFactor.max).toBe(3);
      expect(schema.adjustmentFactor.step).toBe(0.1);
    });
  });

  describe("动量策略 (momentum)", () => {
    const strategy = strategies.momentum;

    it("应该有正确的基本信息", () => {
      expect(strategy.id).toBe("momentum");
      expect(strategy.name).toBe("动量策略");
      expect(strategy.riskLevel).toBe("high");
      expect(strategy.complexity).toBe("advanced");
    });

    it("应该有动量指标参数", () => {
      const schema = strategy.parameterSchema;

      expect(schema).toHaveProperty("lookbackPeriod");
      expect(schema).toHaveProperty("threshold");
      expect(schema).toHaveProperty("rebalanceFrequency");

      expect(schema.lookbackPeriod.min).toBe(5);
      expect(schema.lookbackPeriod.max).toBe(252);
      expect(schema.threshold.min).toBe(1);
      expect(schema.threshold.max).toBe(20);
    });
  });

  describe("均值回归策略 (mean_reversion)", () => {
    const strategy = strategies.mean_reversion;

    it("应该有正确的基本信息", () => {
      expect(strategy.id).toBe("mean_reversion");
      expect(strategy.name).toBe("均值回归策略");
      expect(strategy.riskLevel).toBe("medium");
      expect(strategy.complexity).toBe("intermediate");
    });

    it("应该有均值回归参数", () => {
      const schema = strategy.parameterSchema;

      expect(schema).toHaveProperty("movingAveragePeriod");
      expect(schema).toHaveProperty("deviationThreshold");
      expect(schema).toHaveProperty("rebalanceFrequency");

      expect(schema.movingAveragePeriod.min).toBe(5);
      expect(schema.movingAveragePeriod.max).toBe(252);
      expect(schema.deviationThreshold.min).toBe(1);
      expect(schema.deviationThreshold.max).toBe(30);
    });
  });

  describe("getStrategy函数", () => {
    it("应该返回指定的策略", () => {
      const strategy = getStrategy("fixed_amount");
      expect(strategy).toBeDefined();
      expect(strategy.id).toBe("fixed_amount");
    });

    it("应该处理不存在的策略", () => {
      const strategy = getStrategy("non_existent" as StrategyType);
      expect(strategy).toBeUndefined();
    });

    it("应该返回所有策略", () => {
      const allStrategies = getStrategies();
      expect(Array.isArray(allStrategies)).toBe(true);
      expect(allStrategies.length).toBe(6);

      const strategyIds = allStrategies.map((s) => s.id);
      expect(strategyIds).toContain("fixed_amount");
      expect(strategyIds).toContain("value_averaging");
      expect(strategyIds).toContain("smart_fixed");
      expect(strategyIds).toContain("grid_trading");
      expect(strategyIds).toContain("momentum");
      expect(strategyIds).toContain("mean_reversion");
    });
  });

  describe("参数验证", () => {
    it("应该验证数值参数的范围", () => {
      for (const strategy of Object.values(strategies)) {
        for (const parameter of Object.values(strategy.parameterSchema)) {
          if (parameter.type === "number" && parameter.min !== undefined) {
            expect(parameter.min).toBeGreaterThanOrEqual(0);
          }

          if (
            parameter.type === "number" &&
            parameter.max !== undefined &&
            parameter.min !== undefined
          ) {
            expect(parameter.max).toBeGreaterThan(parameter.min);
          }

          if (
            parameter.type === "number" &&
            parameter.defaultValue !== undefined &&
            parameter.min !== undefined
          ) {
            expect(parameter.defaultValue).toBeGreaterThanOrEqual(parameter.min);
          }

          if (
            parameter.type === "number" &&
            parameter.defaultValue !== undefined &&
            parameter.max !== undefined
          ) {
            expect(parameter.defaultValue).toBeLessThanOrEqual(parameter.max);
          }
        }
      }
    });

    it("应该验证选择参数的选项", () => {
      for (const strategy of Object.values(strategies)) {
        for (const parameter of Object.values(strategy.parameterSchema)) {
          if (parameter.type === "select" && parameter.options) {
            expect(Array.isArray(parameter.options)).toBe(true);
            expect(parameter.options.length).toBeGreaterThan(0);

            if (parameter.defaultValue) {
              const optionValues = parameter.options.map((opt) => opt.value);
              expect(optionValues).toContain(parameter.defaultValue);
            }
          }
        }
      }
    });

    it("应该验证日期参数的格式", () => {
      for (const strategy of Object.values(strategies)) {
        for (const parameter of Object.values(strategy.parameterSchema)) {
          if (parameter.type === "date" && parameter.defaultValue) {
            expect(parameter.defaultValue).toMatch(/^\d{4}-\d{2}-\d{2}$/);
          }
        }
      }
    });
  });
});

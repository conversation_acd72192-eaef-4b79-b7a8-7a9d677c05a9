"use server";

import type { Strategy, StrategyType } from "@/types/fund";
import { strategyLogger as logger } from "./logger";
import { strategies } from "./strategyData";

/**
 * Server action to get all strategies
 */
export async function getStrategiesAction(): Promise<Strategy[]> {
  logger.debug("获取所有策略");
  return Object.values(strategies);
}

/**
 * Server action to get a strategy by ID
 */
export async function getStrategyByIdAction(id: string): Promise<Strategy | null> {
  logger.debug("根据ID获取策略", { id });
  
  // Check if id is a valid strategy type
  if (id in strategies) {
    return strategies[id as StrategyType];
  }
  
  logger.warn("未找到策略", { id });
  return null;
}

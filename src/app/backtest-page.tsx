import { Suspense } from "react";
import BacktestForm from "@/components/backtest-form";
import BacktestResults from "@/components/backtest-results";
import { getStrategiesAction } from "@/lib/strategies";
import LoadingSpinner from "@/components/ui/loading-spinner";

/**
 * Server Component for the backtest page
 * Fetches initial data and renders the backtest form and results
 */
export default async function BacktestPage({
  searchParams,
}: {
  searchParams?: {
    [key: string]: string | string[] | undefined;
  };
}) {
  // Convert searchParams to the expected format
  const processedParameters = Object.entries(searchParams || {}).reduce(
    (accumulator, [key, value]) => {
      // Handle array values (take the first one)
      accumulator[key] = Array.isArray(value) ? value[0] : value;
      return accumulator;
    },
    {} as Record<string, string | undefined>
  );
  
  // Fetch strategies on the server
  const strategies = await getStrategiesAction();
  
  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                基金投资策略回测计算器
              </h1>
              <p className="mt-2 text-gray-600">
                选择基金和投资策略，分析历史表现
              </p>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 左侧配置面板 */}
          <div className="lg:col-span-1 space-y-6">
            <Suspense fallback={<LoadingSpinner />}>
              <BacktestForm strategies={strategies} />
            </Suspense>
          </div>

          {/* 右侧结果展示 */}
          <div className="lg:col-span-2">
            <Suspense fallback={<LoadingSpinner />}>
              <BacktestResults searchParams={processedParameters} />
            </Suspense>
          </div>
        </div>
      </main>
    </div>
  );
}